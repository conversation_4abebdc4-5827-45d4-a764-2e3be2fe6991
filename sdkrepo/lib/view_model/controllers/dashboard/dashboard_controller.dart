import 'package:digital_onboarding/digital_onboarding.dart';
import 'package:example/view/common/InformationalScreen.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../resources/exports/index.dart';

class DashboardController extends GetxController {
  late List<DashboardCardsModel> cards;
  DigitalOnBoardingGetSessionModel? session;

  final RxInt currentPageIndex = 0.obs;

  DigitalOnBoardingSessionDataModel? sessiondata;

  LatLng? selectedLoc;
  bool showSummary = false;
  bool showThankYou = false;

  bool allStepsVerified() {
    if (session == null) return false;
    if ((session?.login ?? false) && (session?.selfie ?? false) && (session?.document ?? false) && (session?.question ?? false) && (session?.locationdata ?? false) && (session?.signature ?? false)) {
      return true;
    } else {
      return false;
    }
  }

  Future<void> viewSummary() async {
    await getSummary();
  }

  hideSummary() {
    showSummary = false;
    update(['page_update']);
  }

  Future<void> onCompleteKycTap() async {
    if (allStepsVerified()) {
      await AuthManager.instance.logout(shouldNavigate: false);
      showThankYou = true;
      update(['page_update']);
    } else {
      return CustomSnackBar.errorSnackBar(message: Strings.PLEASE_COMPLETE);
    }
  }

  Future<void> getSummary() async {
    try {
      sessiondata = await DigitalOnboardingServices.getSessionData();

      showSummary = true;
      update(['page_update']);
    } on DigitalOnboardingException {
      return CustomSnackBar.errorSnackBar(
        message: Strings.SOMETHING_WENT_WRONG,
      );
    }
  }

  String getMapKey() {
    return Platform.isAndroid ? FlutterConfig.get('ANDROID_GOOGLE_MAPS_API_KEY') : FlutterConfig.get('IOS_GOOGLE_MAPS_API_KEY');
  }

  Future<void> updateLocation(Map<String, dynamic>? locationData) async {
    GlobalHelper.showPopupLoader();

    try {
      double lat = locationData?['lat'];
      double lng = locationData?['lng'];

      await DigitalOnboardingServices.uploadLocationData(
          address: locationData?["address"],
          lat: lat,
          lng: lng,
          hostNext: "",
          streetAddress1: locationData?["streetAddress1"],
          streetAddress2: locationData?['streetAddress2'],
          city: locationData?['city'],
          country: locationData?['country'],
          postalCode: locationData?['postalCode']);

      Get.back();

      CustomSnackBar.successSnackBar(message: "Document Uploaded Successfully");

      getSession();
    } on DigitalOnboardingException {
      Get.back();
    } catch (e) {
      Get.back();
    }
  }

  Future<void> updateDocs(XFile image) async {
    Get.back();

    GlobalHelper.showPopupLoader();

    await DigitalOnboardingServices.submitDocument(image, step: StepEnum.extraDoc);

    Get.back();

    getSession();
  }

  void initializeData() {
    cards = [
      DashboardCardsModel(
        id: 0,
        name: Strings.VERIFY_BY,
        onTap: () => Get.toNamed(Routes.LOGIN),
        isCompleted: session?.login ?? false,
        isAvailable: true,
      ),
      DashboardCardsModel(
        id: 1,
        name: Strings.SELFIE_VERIFICTION,
        onTap: () => Get.to(
          Informationalscreen(
            imageOption: InformationalImageOptions.selfie,
            title: "Smile and blink to the camera",
            orderSubtitleList: const [
              "Hold your device vertically",
              "Make sure you have good lighting",
              "Blink your eyes",
              "Smile to the camera",
            ],
            onSubmit: () {
              Get.toNamed(Routes.SELFIE_VERIFICATION);
            },
          ),
        ),
        // onTap: () => Get.toNamed(Routes.SELFIE_VERIFICATION),
        isCompleted: session?.selfie ?? false,
        isAvailable: (session?.login ?? false),
      ),
      DashboardCardsModel(
        id: 2,
        name: Strings.DOCUMENT_VERIFICATION,
        onTap: () => Get.toNamed(Routes.DOC_VERIFICATION),
        isCompleted: session?.document ?? false,
        isAvailable: (session?.selfie ?? false),
        imageUrl: "assets/images/Document.svg",
      ),
      DashboardCardsModel(
        id: 3,
        name: Strings.NFC_VERIFICATION,
        onTap: () => Get.toNamed(Routes.NFC),
        isCompleted: session?.nfc ?? false,
        isAvailable: session?.document ?? false,
        imageUrl: "assets/images/NFC.svg",
      ),
      DashboardCardsModel(
        id: 4,
        name: Strings.Location_GET,
        onTap: () => Get.to(
          () => PlacePicker(
            apiKey: getMapKey(),
            onPlacePicked: (result) {
              Get.toNamed(
                Routes.ADDRESS_PAGE,
                arguments: {
                  'address': result.formattedAddress,
                  'lat': result.geometry?.location.lat,
                  'lng': result.geometry?.location.lng,
                },
              );
              // Get.back();
              // updateLocation({
              //   'address': result.formattedAddress,
              //   'lat': result.geometry?.location.lat,
              //   'lng': result.geometry?.location.lng
              // });
            },
            initialPosition: selectedLoc ?? const LatLng(35.566864, 45.416107),
            useCurrentLocation: selectedLoc == null ? true : false,
          ),
        ),
        imageUrl: "assets/images/Address.svg",
        isCompleted: session?.locationdata ?? false,
        isAvailable: (session?.document ?? false),
      ),
      DashboardCardsModel(
        id: 5,
        name: Strings.QUESTIONS,
        onTap: () => Get.toNamed(Routes.QUESTIONS),
        isCompleted: session?.question ?? false,
        isAvailable: (session?.locationdata ?? false),
        imageUrl: "assets/images/Questions.svg",
      ),
      DashboardCardsModel(
        id: 6,
        name: Strings.SIGNATURE,
        onTap: () => Get.toNamed(Routes.SIGNATURE),
        isCompleted: session?.signature ?? false,
        isAvailable: (session?.locationdata ?? false),
        imageUrl: "assets/images/signiture.svg",
      ),
      DashboardCardsModel(
        id: 7,
        name: Strings.Extra_DOCS,
        onTap: () => Get.toNamed(Routes.EXTRA_DOCUMENTS),
        isCompleted: session?.extradocuments ?? false,
        isAvailable: (session?.locationdata ?? false),
        imageUrl: "assets/images/extra docs.svg",
      ),
    ];
  }

  void pickImage() async {
    XFile? image = await ImagePickerService.pickImage(imageSource: ImageSource.camera);
    if (image != null) {
      CustomDialog.showDialog(
        onSave: () => updateDocs(image),
        title: Strings.Extra_DOCS,
        content: Container(
          color: AppColors.white,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ImageService.image(
                  image.path,
                  borderRadius: 12.0,
                  imageHeight: 200,
                  imageWidth: 200,
                ),
              ],
            ),
          ),
        ),
      );
    }
  }

  Future<void> getSession() async {
    session = await DigitalOnboardingServices.getSession();

    if (session == null) {
      return CustomSnackBar.errorSnackBar(
        message: Strings.SOMETHING_WENT_WRONG,
      );
    }

    log.w(session?.toJson());

    initializeData();

    update(['verification_card']);
  }

  logout() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove("USER_NAME");
    await prefs.remove("PASSWORD");

    Get.offNamed(Routes.MERCHANT_LOGIN);
  }

  @override
  void onInit() {
    getSession();
    initializeData();
    super.onInit();
  }
}
