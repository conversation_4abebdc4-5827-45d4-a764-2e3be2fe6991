import 'package:example/view/dashboard/tabs/dashboard_home_tab.dart';
import 'package:example/view/dashboard/tabs/setting_tab.dart';
import 'package:flutter/cupertino.dart';

import '../../resources/exports/index.dart';
import 'widgets/summary_detail.dart';

class Dashboard extends GetView<DashboardController> {
  const Dashboard({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF9FDFF),
      body: Obx(
        () {
          return <Widget>[
            const DashboardHomeTab(),
            const ShowSummary(),
            const SettingTab(),
          ][controller.currentPageIndex.value];
        },
      ),
      bottomNavigationBar: GetBuilder<DashboardController>(
        id: 'page_update',
        builder: (_) {
          return Obx(
            () => NavigationBar(
              selectedIndex: controller.currentPageIndex.value,
              onDestinationSelected: (value) => controller.currentPageIndex.value = value,
              destinations: const [
                NavigationDestination(
                  selectedIcon: Icon(CupertinoIcons.home),
                  icon: Icon(CupertinoIcons.home),
                  label: 'Home',
                ),
                NavigationDestination(
                  selectedIcon: Icon(CupertinoIcons.list_dash),
                  icon: Icon(CupertinoIcons.list_bullet),
                  label: 'Summery',
                ),
                NavigationDestination(
                  selectedIcon: Icon(CupertinoIcons.settings_solid),
                  icon: Icon(CupertinoIcons.settings),
                  label: 'Settings',
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
