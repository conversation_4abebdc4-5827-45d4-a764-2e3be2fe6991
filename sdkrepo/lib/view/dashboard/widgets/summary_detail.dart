import '../../../resources/exports/index.dart';

class ShowSummary extends GetView<DashboardController> {
  const ShowSummary({super.key});

  @override
  Widget build(BuildContext context) {
    String questionText = "";
    if (controller.sessiondata?.questions.isNotEmpty ?? false) {
      for (int i = 0; i < controller.sessiondata!.questions.length; i++) {
        questionText += '${i + 1}: ${controller.sessiondata!.questions[i]["answer_text"]}\n';
      }

      controller.sessiondata!.questions.map((e) {
        questionText += '$key: ${e["answer_text"]}\n';
      });

      if ((controller.sessiondata?.nfcImage.isNotEmpty ?? false) && (controller.sessiondata!.nfc?.isNotEmpty ?? false)) {
        jsonDecode(controller.sessiondata!.nfc!).forEach((key, value) {});
      }
    }
    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      child: <PERSON>umn(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (controller.sessiondata?.loginType?.isNotEmpty ?? false)
            Card(
                child: ExpansionTile(
              shape: Border.all(color: Colors.transparent),
              title: Text(Strings.VERIFY_BY, style: context.labelLarge),
              children: [
                Text("Login Type : ${controller.sessiondata!.loginType!}", style: context.titleMedium),
                const SpaceH4(),
                Text("${controller.sessiondata!.loginType!} : ${controller.sessiondata!.emailOrPhone!}", style: context.titleMedium),
                const SpaceH16()
              ],
            )),
          if (controller.sessiondata?.selfie.isNotEmpty ?? false)
            Card(
                child: ExpansionTile(
              shape: Border.all(color: Colors.transparent),
              title: Text(Strings.SELFIE_VERIFICTION, style: context.labelLarge),
              children: [
                GestureDetector(
                    onTap: () => {Get.toNamed(Routes.PHOTOVIEW, arguments: controller.sessiondata!.selfie[0].documentPath)},
                    child: ImageService.image(
                      controller.sessiondata!.selfie[0].documentPath,
                      borderRadius: 12.0,
                      imageHeight: 174,
                      imageWidth: 174,
                    )),
                const SpaceH16()
              ],
            )),
          if (controller.sessiondata?.documents.isNotEmpty ?? false)
            Card(
                child: ExpansionTile(
              shape: Border.all(color: Colors.transparent),
              title: Text(Strings.DOCUMENT_VERIFICATION, style: context.labelLarge),
              children: [
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      ...controller.sessiondata!.documents.map(
                        (data) => Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: GestureDetector(
                            onTap: () => {Get.toNamed(Routes.PHOTOVIEW, arguments: data.documentPath)},
                            child: ImageService.image(
                              data.documentPath,
                              borderRadius: 12.0,
                              imageHeight: 174,
                              imageWidth: 174,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SpaceH16()
              ],
            )),
          if (controller.sessiondata?.nfc?.isNotEmpty ?? false)
            Card(
                child: ExpansionTile(
              shape: Border.all(color: Colors.transparent),
              title: Text(Strings.NFC_VERIFICATION, style: context.labelLarge),
              children: [
                GestureDetector(
                    onTap: () => {Get.toNamed(Routes.PHOTOVIEW, arguments: controller.sessiondata!.nfcImage[0].documentPath)},
                    child: ImageService.image(
                      controller.sessiondata!.nfcImage[0].documentPath,
                      borderRadius: 12.0,
                      imageHeight: 174,
                      imageWidth: 174,
                    )),
                const SpaceH16(),
                Text(controller.sessiondata!.nfc ?? "", style: context.titleMedium, textAlign: TextAlign.center).paddingSymmetric(horizontal: 16.0, vertical: 0.0),
                const SpaceH4()
              ],
            )),
          if (controller.sessiondata?.locationData?.addressData?.address?.isNotEmpty ?? false)
            Card(
                child: ExpansionTile(
              shape: Border.all(color: Colors.transparent),
              title: Text(Strings.ADDRESS, style: context.labelLarge),
              children: [
                Padding(padding: const EdgeInsets.symmetric(horizontal: 15.0), child: Text(controller.sessiondata!.locationData!.addressData!.address ?? "", style: context.titleMedium)),
                const SpaceH4(),
                SizedBox(
                  height: 400.0,
                  width: double.maxFinite,
                  child: GoogleMap(
                    mapType: MapType.normal,
                    // ignore: prefer_collection_literals
                    markers: Set()
                      ..add(
                        Marker(
                          markerId: const MarkerId(
                              //todo add session id here
                              ""),
                          position: LatLng(
                            controller.sessiondata!.locationData!.addressData!.lat! as double,
                            controller.sessiondata!.locationData!.addressData!.lng! as double,
                          ),
                          icon: BitmapDescriptor.defaultMarker,
                        ),
                      ),
                    scrollGesturesEnabled: false,
                    zoomControlsEnabled: false,
                    myLocationEnabled: false,
                    initialCameraPosition: CameraPosition(
                      target: LatLng(
                        controller.sessiondata!.locationData!.addressData!.lat! as double,
                        controller.sessiondata!.locationData!.addressData!.lng! as double,
                      ),
                      zoom: 14,
                    ),
                  ),
                ),
              ],
            )),
          if (questionText.isNotEmpty)
            Card(
                child: ExpansionTile(
              shape: Border.all(color: Colors.transparent),
              title: Text(Strings.QUESTIONS, style: context.labelLarge),
              children: [Text(questionText, style: context.titleMedium, textAlign: TextAlign.center).paddingSymmetric(horizontal: 16.0)],
            )),
          if (controller.sessiondata?.signature.isNotEmpty ?? false)
            Card(
                child: ExpansionTile(
              shape: Border.all(color: Colors.transparent),
              title: Text(Strings.SIGNATURE, style: context.labelLarge),
              children: [
                GestureDetector(
                    onTap: () => {Get.toNamed(Routes.PHOTOVIEW, arguments: controller.sessiondata!.signature[0].documentPath)},
                    child: ImageService.image(
                      controller.sessiondata!.signature[0].documentPath,
                      borderRadius: 12.0,
                      imageHeight: 174,
                      imageWidth: 174,
                    )),
                const SpaceH16()
              ],
            )),
          if (controller.sessiondata?.extradocuments.isNotEmpty ?? false)
            Card(
              child: ExpansionTile(
                shape: Border.all(color: Colors.transparent),
                title: Text(Strings.Extra_DOCS_Summary, style: context.labelLarge),
                children: [
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        ...controller.sessiondata!.extradocuments.map(
                          (data) => Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 8.0),
                            child: GestureDetector(
                              onTap: () => {Get.toNamed(Routes.PHOTOVIEW, arguments: data.documentPath)},
                              child: ImageService.image(
                                data.documentPath,
                                borderRadius: 12.0,
                                imageHeight: 174,
                                imageWidth: 174,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SpaceH16()
                ],
              ),
            ),
        ],
      ),
    ).paddingSymmetric(horizontal: 16.0, vertical: 12.0);
  }
}
