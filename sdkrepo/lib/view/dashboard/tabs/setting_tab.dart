import 'package:example/resources/exports/index.dart';
import 'package:flutter/cupertino.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingTab extends StatelessWidget {
  const SettingTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Setting"),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            children: [
              const SpaceH32(),
              CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () async {
                  final SharedPreferences prefs = await SharedPreferences.getInstance();
                  await prefs.remove("USER_NAME");
                  await prefs.remove("PASSWORD");

                  Get.offNamed(Routes.MERCHANT_LOGIN);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 15),
                  width: double.maxFinite,
                  clipBehavior: Clip.antiAlias,
                  decoration: ShapeDecoration(
                    color: const Color(0xffE8E0EB),
                    shape: ContinuousRectangleBorder(
                      borderRadius: BorderRadius.circular(24.0),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      DefaultTextStyle(
                        style: TextStyle(
                          color: Color(0xff4A454E),
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        child: Text("Logout"),
                      ),
                      Icon(
                        // Icons.logout_outlined,
                        CupertinoIcons.square_arrow_left,
                        color: Color(0xff4A454E),
                        size: 24,
                      ),
                    ],
                  ),
                ),
              ),
              const SpaceH16(),
              CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {},
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 15),
                  width: double.maxFinite,
                  clipBehavior: Clip.antiAlias,
                  decoration: ShapeDecoration(
                    color: const Color(0xffE8E0EB),
                    shape: ContinuousRectangleBorder(
                      borderRadius: BorderRadius.circular(24.0),
                    ),
                  ),
                  alignment: Alignment.center,
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      DefaultTextStyle(
                        style: TextStyle(
                          color: Color(0xff4A454E),
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        child: Text("Reset Session"),
                      ),
                      Icon(
                        CupertinoIcons.restart,
                        color: Color(0xff4A454E),
                        size: 24,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
