import 'package:example/resources/exports/index.dart';

class SummaryTab extends GetView<DashboardController> {
  const SummaryTab({super.key});

  @override
  Widget build(BuildContext context) {
    // Automatically fetch summary data when this widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (controller.sessiondata == null) {
        controller.getSummary();
      }
    });

    return Scaffold(
      backgroundColor: const Color(0xffF9FDFF),
      appBar: const CustomAppBar(title: Strings.Summary),
      body: GetBuilder<DashboardController>(
        id: 'page_update',
        builder: (_) {
          // Show loading indicator while fetching data
          if (controller.sessiondata == null) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Login Verification Section
                if (controller.sessiondata?.loginType?.isNotEmpty ?? false)
                  Card(
                    child: ExpansionTile(
                      shape: Border.all(color: Colors.transparent),
                      title: Text(Strings.VERIFY_BY, style: context.labelLarge),
                      children: [
                        Text("Login Type : ${controller.sessiondata!.loginType!}", style: context.titleMedium),
                        const SpaceH4(),
                        Text("${controller.sessiondata!.loginType!} : ${controller.sessiondata!.emailOrPhone!}", style: context.titleMedium),
                        const SpaceH16()
                      ],
                    ),
                  ),

                // Selfie Verification Section
                if (controller.sessiondata?.selfie.isNotEmpty ?? false)
                  Card(
                    child: ExpansionTile(
                      shape: Border.all(color: Colors.transparent),
                      title: Text(Strings.SELFIE_VERIFICTION, style: context.labelLarge),
                      children: [
                        GestureDetector(
                          onTap: () => Get.toNamed(Routes.PHOTOVIEW, arguments: controller.sessiondata!.selfie[0].documentPath),
                          child: ImageService.image(
                            controller.sessiondata!.selfie[0].documentPath,
                            borderRadius: 12.0,
                            imageHeight: 174,
                            imageWidth: 174,
                          ),
                        ),
                        const SpaceH16()
                      ],
                    ),
                  ),

                // Document Verification Section
                if (controller.sessiondata?.documents.isNotEmpty ?? false)
                  Card(
                    child: ExpansionTile(
                      shape: Border.all(color: Colors.transparent),
                      title: Text(Strings.DOCUMENT_VERIFICATION, style: context.labelLarge),
                      children: [
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: [
                              ...controller.sessiondata!.documents.map(
                                (data) => Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                                  child: GestureDetector(
                                    onTap: () => Get.toNamed(Routes.PHOTOVIEW, arguments: data.documentPath),
                                    child: ImageService.image(
                                      data.documentPath,
                                      borderRadius: 12.0,
                                      imageHeight: 174,
                                      imageWidth: 174,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SpaceH16()
                      ],
                    ),
                  ),

                // NFC Verification Section
                if (controller.sessiondata?.nfc?.isNotEmpty ?? false)
                  Card(
                    child: ExpansionTile(
                      shape: Border.all(color: Colors.transparent),
                      title: Text(Strings.NFC_VERIFICATION, style: context.labelLarge),
                      children: [
                        if (controller.sessiondata?.nfcImage.isNotEmpty ?? false)
                          GestureDetector(
                            onTap: () => Get.toNamed(Routes.PHOTOVIEW, arguments: controller.sessiondata!.nfcImage[0].documentPath),
                            child: ImageService.image(
                              controller.sessiondata!.nfcImage[0].documentPath,
                              borderRadius: 12.0,
                              imageHeight: 174,
                              imageWidth: 174,
                            ),
                          ),
                        const SpaceH16(),
                        Text(controller.sessiondata!.nfc ?? "", style: context.titleMedium, textAlign: TextAlign.center)
                            .paddingSymmetric(horizontal: 16.0, vertical: 0.0),
                        const SpaceH4()
                      ],
                    ),
                  ),

                // Questions Section
                if (controller.sessiondata?.questions.isNotEmpty ?? false)
                  Card(
                    child: ExpansionTile(
                      shape: Border.all(color: Colors.transparent),
                      title: Text(Strings.QUESTIONS, style: context.labelLarge),
                      children: [
                        ...controller.sessiondata!.questions.asMap().entries.map(
                          (entry) => Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
                            child: Text(
                              '${entry.key + 1}: ${entry.value["answer_text"]}',
                              style: context.titleMedium,
                            ),
                          ),
                        ),
                        const SpaceH16()
                      ],
                    ),
                  ),

                // Signature Section
                if (controller.sessiondata?.signature.isNotEmpty ?? false)
                  Card(
                    child: ExpansionTile(
                      shape: Border.all(color: Colors.transparent),
                      title: Text(Strings.SIGNATURE, style: context.labelLarge),
                      children: [
                        GestureDetector(
                          onTap: () => Get.toNamed(Routes.PHOTOVIEW, arguments: controller.sessiondata!.signature[0].documentPath),
                          child: ImageService.image(
                            controller.sessiondata!.signature[0].documentPath,
                            borderRadius: 12.0,
                            imageHeight: 174,
                            imageWidth: 174,
                          ),
                        ),
                        const SpaceH16()
                      ],
                    ),
                  ),

                // Address Section
                if (controller.sessiondata?.locationData?.addressData?.address?.isNotEmpty ?? false)
                  Card(
                    child: ExpansionTile(
                      shape: Border.all(color: Colors.transparent),
                      title: Text(Strings.ADDRESS, style: context.labelLarge),
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 15.0),
                          child: Text(controller.sessiondata!.locationData!.addressData!.address ?? "", style: context.titleMedium),
                        ),
                        const SpaceH4(),
                        SizedBox(
                          height: 400.0,
                          width: double.maxFinite,
                          child: GoogleMap(
                            mapType: MapType.normal,
                            markers: {
                              Marker(
                                markerId: const MarkerId("location"),
                                position: LatLng(
                                  controller.sessiondata!.locationData!.addressData!.lat! as double,
                                  controller.sessiondata!.locationData!.addressData!.lng! as double,
                                ),
                              ),
                            },
                            initialCameraPosition: CameraPosition(
                              target: LatLng(
                                controller.sessiondata!.locationData!.addressData!.lat! as double,
                                controller.sessiondata!.locationData!.addressData!.lng! as double,
                              ),
                              zoom: 14.0,
                            ),
                            onMapCreated: (GoogleMapController mapController) {},
                          ),
                        ),
                        const SpaceH16()
                      ],
                    ),
                  ),

                // Extra Documents Section
                if (controller.sessiondata?.extradocuments.isNotEmpty ?? false)
                  Card(
                    child: ExpansionTile(
                      shape: Border.all(color: Colors.transparent),
                      title: Text(Strings.Extra_DOCS_Summary, style: context.labelLarge),
                      children: [
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: [
                              ...controller.sessiondata!.extradocuments.map(
                                (data) => Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                                  child: GestureDetector(
                                    onTap: () => Get.toNamed(Routes.PHOTOVIEW, arguments: data.documentPath),
                                    child: ImageService.image(
                                      data.documentPath,
                                      borderRadius: 12.0,
                                      imageHeight: 174,
                                      imageWidth: 174,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SpaceH16()
                      ],
                    ),
                  ),
              ],
            ).paddingSymmetric(horizontal: 16.0, vertical: 12.0),
          );
        },
      ),
    );
  }
}
