import 'package:example/resources/exports/index.dart';
import 'package:example/view/dashboard/widgets/card_view.dart';
import 'package:example/view/dashboard/widgets/selfie_card_view.dart';
import 'package:example/view/dashboard/widgets/summary_detail.dart';
import 'package:example/view/dashboard/widgets/verify_otp_card_view.dart';
import 'package:flutter/cupertino.dart';

class DashboardHomeTab extends GetView<DashboardController> {
  const DashboardHomeTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF9FDFF),
      appBar: CustomAppBar(title: controller.showSummary ? (controller.showThankYou ? Strings.THANK_YOU : Strings.Summary) : Strings.VERIFICATION_STEPS),
      body: GetBuilder<DashboardController>(
        id: 'page_update',
        builder: (_) {
          return controller.showSummary
              ? (controller.showThankYou
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text("Thank You", style: context.headlineLarge),
                          Text("Your Application Has Been Submitted", style: context.titleMedium),
                        ],
                      ),
                    )
                  : const ShowSummary())
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                    children: [
                      TextButton(
                        onPressed: () {
                          Get.offAllNamed(Routes.HOME);
                        },
                        child: Text("data"),
                      ),

                      GetBuilder<DashboardController>(
                        id: 'page_update',
                        builder: (_) {
                          return controller.showSummary
                              ? (controller.showThankYou
                                  ? Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
                                      child: CustomButton.solid(
                                        backgroundColor: AppColors.primary,
                                        textColor: const Color.fromRGBO(255, 255, 255, 1),
                                        text: Strings.CONTINUE,
                                        onTapAsync: () async => Get.offAllNamed(Routes.HOME),
                                        radius: Sizes.RADIUS_12,
                                        constraints: const BoxConstraints(minHeight: 55),
                                      ),
                                    )
                                  : Column(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
                                          child: CustomButton.solid(
                                            backgroundColor: AppColors.primary,
                                            textColor: const Color.fromRGBO(255, 255, 255, 1),
                                            text: Strings.BACK,
                                            onTap: () => controller.hideSummary(),
                                            radius: Sizes.RADIUS_12,
                                            constraints: const BoxConstraints(minHeight: 55),
                                          ),
                                        ),
                                      ],
                                    ))
                              : CustomButton.solid(
                                  backgroundColor: AppColors.primary,
                                  textColor: AppColors.white,
                                  text: Strings.SHOW_SUMMARY,
                                  onTapAsync: () async => controller.viewSummary(),
                                  radius: Sizes.RADIUS_12,
                                  constraints: const BoxConstraints(minHeight: 55),
                                ).paddingSymmetric(
                                  horizontal: 16.0,
                                  vertical: 12.0,
                                );
                        },
                      ),

                      GetBuilder<DashboardController>(
                        id: 'verification_card',
                        builder: (context) {
                          var card = controller.cards[0];
                          return VerifyOtpCardView(
                            isCompleted: card.isCompleted,
                            onPress: card.isAvailable ? card.onTap : null,
                          );
                        },
                      ),
                      const SpaceH24(),

                      // Second card - Selfie
                      GetBuilder<DashboardController>(
                        id: 'verification_card',
                        builder: (context) {
                          return SelfieCardView(
                            isCompleted: controller.cards[1].isCompleted,
                            onPress: controller.cards[1].isAvailable ? controller.cards[1].onTap : null,
                          );
                        },
                      ),
                      const SpaceH24(),
                      // Next two cards in GridView (index 2 & 3)
                      if (controller.cards.length > 3) ...[
                        GridView(
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            mainAxisSpacing: 0,
                            crossAxisSpacing: 16.0,
                            childAspectRatio: 1.36,
                          ),
                          children: [
                            // Card at index 2
                            GetBuilder<DashboardController>(
                              id: 'verification_card',
                              builder: (_) {
                                var card = controller.cards[2];
                                return CardView(
                                  isCompleted: card.isCompleted,
                                  onPress: card.isAvailable ? card.onTap : null,
                                  title: card.name,
                                  iconPath: card.imageUrl ?? "",
                                );
                              },
                            ),
                            // Card at index 3
                            GetBuilder<DashboardController>(
                              id: 'verification_card',
                              builder: (_) {
                                var card = controller.cards[3];
                                return CardView(
                                  isCompleted: card.isCompleted,
                                  onPress: card.isAvailable ? card.onTap : null,
                                  title: card.name,
                                  iconPath: card.imageUrl ?? "",
                                );
                              },
                            ),
                          ],
                        ),
                        const SpaceH16(),

                        // Divider
                        const Divider(
                          color: AppColors.disabled,
                          thickness: 1.0,
                          height: 32.0,
                        ),
                        const SpaceH16(),

                        // Remaining cards in GridView (4 cards) - starting from index 4
                        if (controller.cards.length > 4) ...[
                          GridView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 2,
                              mainAxisSpacing: 16.0,
                              crossAxisSpacing: 16.0,
                              childAspectRatio: 1.36,
                            ),
                            itemCount: controller.cards.length - 4, // Remaining cards after first 4
                            itemBuilder: (context, index) {
                              int cardIndex = index + 4;
                              return GetBuilder<DashboardController>(
                                id: 'verification_card',
                                builder: (_) {
                                  return CardView(
                                    isCompleted: controller.cards[cardIndex].isCompleted,
                                    onPress: controller.cards[cardIndex].onTap,
                                    title: controller.cards[cardIndex].name,
                                    iconPath: controller.cards[cardIndex].imageUrl ?? "",
                                  );
                                },
                              );
                            },
                          ),
                        ],
                      ],
                    ],
                  ),
                );
        },
      ),
    );
  }
}


/*

GetBuilder<DashboardController>(
              id: 'page_update',
              builder: (_) {
                return controller.showSummary
                    ? (controller.showThankYou
                        ? CustomButton.solid(
                            backgroundColor: AppColors.primary,
                            textColor: const Color.fromRGBO(255, 255, 255, 1),
                            text: Strings.CONTINUE,
                            onTapAsync: () async => Get.offAllNamed(Routes.HOME),
                            radius: Sizes.RADIUS_12,
                            constraints: const BoxConstraints(minHeight: 55),
                          ).paddingSymmetric(horizontal: 16.0, vertical: 12.0)
                        : Column(
                            mainAxisAlignment: MainAxisAlignment.end,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              CustomButton.solid(
                                backgroundColor: AppColors.primary,
                                textColor: const Color.fromRGBO(255, 255, 255, 1),
                                text: Strings.BACK,
                                onTap: () => controller.hideSummary(),
                                radius: Sizes.RADIUS_12,
                                constraints: const BoxConstraints(minHeight: 55),
                              ).paddingSymmetric(horizontal: 16.0, vertical: 12.0),
                            ],
                          ))
                    : CustomButton.solid(
                        backgroundColor: AppColors.primary,
                        textColor: AppColors.white,
                        text: Strings.SHOW_SUMMARY,
                        onTapAsync: () async => controller.viewSummary(),
                        radius: Sizes.RADIUS_12,
                        constraints: const BoxConstraints(minHeight: 55),
                      ).paddingSymmetric(
                        horizontal: 16.0,
                        vertical: 12.0,
                      );
              },
            ),
*/


// bottomNavigationBar: GetBuilder<DashboardController>(
//   id: 'page_update',
//   builder: (_) {
//     return controller.showSummary
//         ? (controller.showThankYou
//             ? CustomButton.solid(
//                 backgroundColor: AppColors.primary,
//                 textColor: const Color.fromRGBO(255, 255, 255, 1),
//                 text: Strings.CONTINUE,
//                 onTapAsync: () async => Get.offAllNamed(Routes.HOME),
//                 radius: Sizes.RADIUS_12,
//                 constraints: const BoxConstraints(minHeight: 55),
//               ).paddingSymmetric(horizontal: 16.0, vertical: 12.0)
//             : Column(
//                 mainAxisAlignment: MainAxisAlignment.end,
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   CustomButton.solid(
//                     backgroundColor: AppColors.primary,
//                     textColor: const Color.fromRGBO(255, 255, 255, 1),
//                     text: Strings.BACK,
//                     onTap: () => controller.hideSummary(),
//                     radius: Sizes.RADIUS_12,
//                     constraints: const BoxConstraints(minHeight: 55),
//                   ).paddingSymmetric(horizontal: 16.0, vertical: 12.0),
//                 ],
//               ))
//         : CustomButton.solid(
//             backgroundColor: AppColors.primary,
//             textColor: AppColors.white,
//             text: Strings.SHOW_SUMMARY,
//             onTapAsync: () async => controller.viewSummary(),
//             radius: Sizes.RADIUS_12,
//             constraints: const BoxConstraints(minHeight: 55),
//           ).paddingSymmetric(horizontal: 16.0, vertical: 12.0);
//   },
// ),